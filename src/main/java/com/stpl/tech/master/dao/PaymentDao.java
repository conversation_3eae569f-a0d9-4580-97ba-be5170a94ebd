package com.stpl.tech.master.dao;

import com.stpl.tech.master.payment.model.PaymentDetail;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface PaymentDao extends MongoRepository<PaymentDetail, String> {


    List<PaymentDetail> findAllByCartId(String cartId);

    PaymentDetail findByExternalPaymentId(String receiptId);

    PaymentDetail findByCartIdAndPaymentStatus(String cartId, String paymentStatus);

    List<PaymentDetail> findAllByTimeAfter(Date time);

}
