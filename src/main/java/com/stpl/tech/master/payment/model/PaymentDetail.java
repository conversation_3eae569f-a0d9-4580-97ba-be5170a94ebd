package com.stpl.tech.master.payment.model;

import com.stpl.tech.master.payment.model.patymNew.PaytmParamResponse;
import com.stpl.tech.master.payment.model.paytm.PaytmCreateRequest;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@Document
public class PaymentDetail {

    @Id
    protected String paymentId;
    @Indexed
    protected String externalPaymentId; // this is external order id
    protected String paymentStatus;
    @Indexed
    protected String cartId;
    protected String sessionId;
    protected BigDecimal amount;
    protected Integer paymentModeId;
    protected Date time;
    protected Date queingTime;

    protected PaytmCreateRequest paytmPayload;

    protected PaytmParamResponse paytmParamResponse;

    protected Integer brandId;
}