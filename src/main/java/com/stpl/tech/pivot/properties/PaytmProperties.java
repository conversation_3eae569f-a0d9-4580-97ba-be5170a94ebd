package com.stpl.tech.pivot.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties("paytm")
@Getter
@Setter
public class PaytmProperties {
    private String merchantId;
    private String merchantKey;
    private String terminalId;
    private String version;
    private String saleApi;
    private String statusApi;
    private String dqrSendApi;
    private String dqrStatusApi;
    private String dqrMerchantId;
    private String dqrMerchantKey;
    private String dqrApiVersion;
    private String dqrClientId;
    private String refundApi;
    private String refundStatusApi;
}
