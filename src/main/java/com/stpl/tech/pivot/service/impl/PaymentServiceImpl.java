package com.stpl.tech.pivot.service.impl;


import com.google.gson.Gson;
import com.stpl.tech.kettle.delivery.model.PaymentPartner;
import com.stpl.tech.master.dao.PaymentDao;
import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.PaymentDetail;
import com.stpl.tech.master.payment.model.PaymentRequest;
import com.stpl.tech.master.payment.model.PaymentRequestStatus;
import com.stpl.tech.master.payment.model.PaymentResponse;
import com.stpl.tech.master.payment.model.PaymentStatus;
import com.stpl.tech.master.payment.model.razorpay.RazorPayCreateRequest;
import com.stpl.tech.pivot.task.PaymentRefundTaskConsumer;
import com.stpl.tech.pivot.core.payment.PaymentPartnerType;
import com.stpl.tech.pivot.core.payment.factory.PaymentFactory;
import com.stpl.tech.pivot.dao.OrderPaymentDetailRepository;
import com.stpl.tech.pivot.data.model.OrderPaymentDetailEntity;
import com.stpl.tech.pivot.domain.OrderPaymentDetail;
import com.stpl.tech.pivot.domain.PayPhiEdcCreateRequest;
import com.stpl.tech.pivot.domain.PayPhiStatusRequest;
import com.stpl.tech.pivot.domain.PaytmEDCStatusResponse;
import com.stpl.tech.pivot.domain.PaytmEdcCreateRequest;
import com.stpl.tech.pivot.domain.PaytmEdcStatusRequest;
import com.stpl.tech.pivot.domain.PaytmRefundRequest;
import com.stpl.tech.pivot.domain.PaytmRefundResponse;
import com.stpl.tech.pivot.domain.PaytmRefundStatusRequest;
import com.stpl.tech.pivot.domain.PaytmRefundStatusResponse;
import com.stpl.tech.pivot.domain.mapper.OrderPaymentDetailMapper;
import com.stpl.tech.pivot.exceptions.BaseException;
import com.stpl.tech.pivot.properties.EnvironmentProperties;
import com.stpl.tech.pivot.service.PaymentService;
import com.stpl.tech.pivot.utils.ApplicationUtils;
import com.stpl.tech.pivot.utils.PaymentUtil;
import com.stpl.tech.util.AppUtils;
import jakarta.annotation.PostConstruct;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import com.stpl.tech.pivot.domain.PaytmDQRCreateRequest;
import com.stpl.tech.pivot.domain.PaytmDQRStatusResponse;
import com.stpl.tech.pivot.domain.PaytmDQRStatusRequest;


import javax.persistence.Query;
import java.net.URISyntaxException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Service
@Log4j2
public class PaymentServiceImpl implements PaymentService {
    @Autowired
    private PaymentFactory paymentFactory;
    @Autowired
    private OrderPaymentDetailRepository orderPaymentDetailRepository;

    private ScheduledExecutorService refundExecutor = Executors.newScheduledThreadPool(1);

    @Autowired
    private EnvironmentProperties env;

    @Value("${is.primary.server:true}")
    private Boolean isPrimaryServer;

    @Autowired
    private PaymentDao paymentDao;

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public PaytmEdcCreateRequest initiateTransactionRequest(OrderPaymentRequest order, PaymentPartnerType paymentPartnerType) throws Exception {
        return (PaytmEdcCreateRequest) paymentFactory.getCreatePaymentAdapter(paymentPartnerType)
                .createPaymentRequest(order, new HashMap<>());

    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public PayPhiEdcCreateRequest initiatePayphiTransactionRequest(OrderPaymentRequest order, PaymentPartnerType paymentPartnerType) throws Exception{
        return (PayPhiEdcCreateRequest) paymentFactory.getCreatePaymentAdapter(paymentPartnerType)
                .createPaymentRequest(order, new HashMap<>());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public PaytmDQRCreateRequest initiateDqrTransactionRequest(OrderPaymentRequest order, PaymentPartnerType paymentPartnerType) throws Exception {
        return (PaytmDQRCreateRequest) paymentFactory.getCreatePaymentAdapter(paymentPartnerType)
                .createPaymentRequest(order, new HashMap<>());

    }

    /**
     * @param transactionId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = true, propagation = Propagation.REQUIRED)
    public OrderPaymentDetail validateOrderPaymentDetail(String transactionId) {
        OrderPaymentDetailEntity orderPaymentDetail = orderPaymentDetailRepository.findByExternalOrderIdAndOrderIdNullAndRequestStatusNot(transactionId,
                PaymentRequestStatus.CANCELLED.name());
        if (Objects.isNull(orderPaymentDetail)) {
            throw new BaseException("Order Payment Not Found For the Transaction Id");
        }
        return OrderPaymentDetailMapper.INSTANCE.toDomain(orderPaymentDetail);
    }

    @Override
    public PaytmEDCStatusResponse updateTransactionRequest(PaytmEdcStatusRequest request, PaymentPartnerType paymentPartnerType) throws Exception {
        return (PaytmEDCStatusResponse) paymentFactory.getCreatePaymentAdapter(paymentPartnerType)
                .updatePayment(request, true);
    }

    @Override
    public PayPhiStatusRequest updatePayPhiTransactionRequest(PayPhiStatusRequest request, PaymentPartnerType paymentPartnerType) throws Exception {
        return (PayPhiStatusRequest) paymentFactory.getCreatePaymentAdapter(paymentPartnerType)
                .updatePayment(request, true);
    }

    @Override
    public PaytmDQRStatusResponse updateDqrTransactionRequest(PaytmDQRStatusRequest request, PaymentPartnerType paymentPartnerType) throws Exception {
        return (PaytmDQRStatusResponse) paymentFactory.getCreatePaymentAdapter(paymentPartnerType)
                .updatePayment(request, true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void updateTransactionStatus(Object response) throws Exception {
        try {
             log.info("Payload for patym webhook call ::: {}", new Gson().toJson(response));
//            if (Objects.nonNull(response)) {
//                OrderPaymentDetailEntity orderPaymentDetail = orderPaymentDetailRepository.findByPartnerOrderId(response.get(ApplicationConstant.ORDERID));
//                if (Objects.nonNull(orderPaymentDetail) && ApplicationConstant.DQR.equals(orderPaymentDetail.getPaymentModeName())) {
//                    if (ApplicationConstant.TXN_SUCCESS.equals(response.get(ApplicationConstant.STATUS))) {
//                        orderPaymentDetail.setPaymentStatus(ApplicationConstant.SUCCESSFUL);
//                        orderPaymentDetail.setPartnerPaymentStatus(response.get(ApplicationConstant.STATUS));
//                    }
//                    if (ApplicationConstant.TXN_FAILURE.equals(response.get(ApplicationConstant.STATUS))) {
//                        orderPaymentDetail.setPaymentStatus(ApplicationConstant.FAILED);
//                        orderPaymentDetail.setPartnerPaymentStatus(response.get(ApplicationConstant.STATUS));
//                    }
//                    orderPaymentDetailRepository.save(orderPaymentDetail);
//                }
//            }
        } catch (BaseException e) {
            log.error("Error while saving final transaction status by webhook call :::::: {}", e);
        }
    }

    public PaytmRefundResponse refundPayment(PaytmRefundRequest refundRequest, PaymentPartnerType paymentPartnerType) throws Exception {
        return (PaytmRefundResponse) paymentFactory.getCreatePaymentAdapter(paymentPartnerType)
                .refundPayment(refundRequest);
    }

    public PaytmRefundStatusResponse getRefundStatus(PaytmRefundStatusRequest refundStatusRequest, PaymentPartnerType paymentPartnerType) throws Exception {
        return (PaytmRefundStatusResponse) paymentFactory.getCreatePaymentAdapter(paymentPartnerType)
                .getRefundStatus(refundStatusRequest);
    }

    @PostConstruct
    void startPaymentTaskConsumer() {
        if(Boolean.TRUE.equals(isPrimaryServer)){
//            log.info("Starting Payment task consumer");
//            PaymentTaskConsumer paymentTaskConsumer = new PaymentTaskConsumer(this, env.getAsyncPaymentRetryDelayInMilli(),env.getAsyncPaymentRefundRetryMaxAttempt());
//            executor.schedule(paymentTaskConsumer, 1, TimeUnit.SECONDS);
            log.info("Starting Payment refund task consumer");
            PaymentRefundTaskConsumer paymentRefundTaskConsumer = new PaymentRefundTaskConsumer(this, env.getAsyncPaymentRefundRetryDelayInMilli());
            refundExecutor.schedule(paymentRefundTaskConsumer, 1, TimeUnit.SECONDS);
//            Date date = ApplicationUtils.timeWithMilliseconds(ApplicationUtils.getCurrentTimeIST(), (-1) * env.getTimeInMilliSecToGetPreviousPayment());
//            reloadPayMap(date);
        }else{
            log.info("Skipping Payment task Consumer");
        }
    }

    public void processAsyncPaymentRefund(int companyId, PaymentRequest paymentRequest, PaymentPartner paymentPartner) {
        PaymentDetail paymentDetailObj = PaymentUtil.paymentDetailMap.get(paymentRequest.getPartnerOrderId());
        log.info("Looking for async payment processing for payment id", paymentDetailObj.getPaymentId());
        synchronized (paymentDetailObj) {
            try {
                initiateRefund(paymentDetailObj, paymentRequest);
            } catch (Exception e) {
                log.error("Error while getting payment detail from razorpay ,", e);
            }
        }
    }

    public void initiateRefund(PaymentDetail paymentDetail, PaymentRequest paymentRequest) {
//        log.info("Initiating refund for customer {} against payment {}",cartDetail.getCustomerId() , paymentDetail.getPaymentId());

        PaymentDetail paymentDetailObj = PaymentUtil.paymentDetailMap.get(paymentDetail.getExternalPaymentId());
        if (Objects.isNull(paymentDetailObj)) {
            paymentDetailObj = paymentDetail;
            // conflict won't arise in this case since automatic order punching would have finished by now
            // it is a rare case
        }
        synchronized (paymentDetailObj) {
            paymentDetail.setPaymentStatus(PaymentStatus.PENDING_REFUND.name());
            paymentDetail = paymentDao.save(paymentDetail);
            paymentDetailObj.setPaymentStatus(PaymentStatus.PENDING_REFUND.name());
        }

//        String text = "Initiate refund for customer " + cartDetail.getCustomerId() + " with payment Id "
//                + paymentDetail.getPaymentId() + " for amount " + paymentDetail.getAmount() + " Rupees ";
//        try {
//            Slack.getInstance().send(env.getEnvType().name(), null, SlackNotification.REFUND, text);
//        } catch (Exception e) {
//            log.error("Error in sending Slack message for Payment refund for payment Id " + paymentDetail.getPaymentId() + " for customer "
//                    + cartDetail.getCustomerId(), e);
//        }
//        log.info(text);

        try{
            PaytmRefundResponse response = new PaytmRefundResponse();
            log.info("Failed to punch wallet order for payment refund for {}", ((PaytmRefundRequest) paymentRequest).getTxnId());
            response.getBody().getResultInfo().setResultStatus(PaymentStatus.PENDING_REFUND.name());
            response.getBody().setTxnId(((PaytmRefundRequest) paymentRequest).getTxnId());
            markPaymentRefunded((PaymentResponse)response);
        } catch (Exception e){
            log.info("Failed to update status of orderPaymentDetail to refund pending for customer {} against payment {}",cartDetail.getCustomerId() , paymentDetail.getPaymentId(),e);
        }

        if (!Objects.isNull(paymentRequest)) {
            punchWalletOrderForRefund(paymentDetailObj, cartDetail, paymentDetail, paymentRequest);
        }
    }

    public OrderPaymentDetail markPaymentRefunded(PaymentResponse response) {
        try {
            LOG.info("Marking payment {} as REFUNDED " ,response.getPartnerOrderId());
            Query query = manager.createQuery("from OrderPaymentDetail E "
                    + "where E.partnerOrderId = :partnerOrderId");
            query.setParameter("partnerOrderId", response.getPartnerOrderId());
            OrderPaymentDetail paymentDetail = (OrderPaymentDetail) query.getSingleResult();
            paymentDetail.setOrderId(Integer.parseInt(response.getOrderId()));
            paymentDetail.setPartnerPaymentStatus(response.getStatus());
            paymentDetail.setPartnerTransactionId(response.getTransactionId());
            paymentDetail.setPaymentStatus(PaymentStatus.REFUND_PROCESSED.name());
            paymentDetail.setResponseTime(AppUtils.getCurrentTimestamp());
            manager.merge(paymentDetail);
            manager.flush();
            createParameters(paymentDetail.getOrderPaymentDetailId(), response.getPersistentAttributes());
            return paymentDetail;
        } catch (Exception e) {
            LOG.error("Could not find active payment detail for external order id: {}", response.getOrderId(), e);
            return null;
        }
    }


}
