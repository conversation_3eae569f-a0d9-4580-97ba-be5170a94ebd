package com.stpl.tech.pivot.service.impl;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

import com.stpl.tech.pivot.domain.PaytmRefundRequest;
import com.stpl.tech.pivot.domain.PaytmRefundResponse;
import com.stpl.tech.pivot.domain.PaytmRefundStatusRequest;
import com.stpl.tech.pivot.domain.PaytmRefundStatusResponse;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.paytm.pg.merchant.PaytmChecksum;
import com.stpl.tech.kettle.delivery.model.PaymentPartner;
import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.PaymentRequestStatus;
import com.stpl.tech.pivot.dao.OrderPaymentDetailRepository;
import com.stpl.tech.pivot.data.model.OrderPaymentDetailEntity;
import com.stpl.tech.pivot.domain.PaytmEDCStatusRequestBody;
import com.stpl.tech.pivot.domain.PaytmEDCStatusResponse;
import com.stpl.tech.pivot.domain.PaytmEDCTransactionRequestHeader;
import com.stpl.tech.pivot.domain.PaytmEDCTransactionResponse;
import com.stpl.tech.pivot.domain.PaytmEdcCreateRequest;
import com.stpl.tech.pivot.domain.PaytmEdcStatusRequest;
import com.stpl.tech.pivot.domain.PaytmDQRStatusRequest;
import com.stpl.tech.pivot.domain.PaytmDQRCreateRequest;
import com.stpl.tech.pivot.domain.PaytmDQRTransactionResponse;
import com.stpl.tech.pivot.domain.PaytmDQRStatusResponse;
import com.stpl.tech.pivot.domain.PaytmDQRRequestHeader;
import com.stpl.tech.pivot.domain.PaymentMode;
import com.stpl.tech.pivot.exceptions.BaseException;
import com.stpl.tech.pivot.properties.PaytmProperties;
import com.stpl.tech.pivot.service.PaytmPaymentService;
import com.stpl.tech.pivot.service.WebClientService;
import com.stpl.tech.pivot.utils.ApplicationConstant;
import com.stpl.tech.pivot.utils.ApplicationUtils;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;

import lombok.extern.log4j.Log4j2;

@Service
@Log4j2
public class PaytmPaymentServiceImpl implements PaytmPaymentService {
	@Autowired
	private WebClientService webClientService;
	@Autowired
	private PaytmProperties paytmProperties;
	@Autowired
	private OrderPaymentDetailRepository orderPaymentDetailRepository;

	@Override
	public PaytmEdcCreateRequest initiateEdcTransaction(OrderPaymentRequest orderPaymentRequest) throws Exception {
		// Get EDC Unit wise Property from Ignite Cache
		PaytmEdcCreateRequest initiateRequest = createPaytmEDCRequest(orderPaymentRequest);
		String paytmChecksum = PaytmChecksum.generateSignature(initiateRequest.getPersistentAttributes(),
				orderPaymentRequest.getMerchantKey());

		JSONObject head = new JSONObject();
		head.put("checksum", paytmChecksum);
		head.put("channelId", "EDC");
		head.put("requestTimeStamp", ApplicationUtils.getCurrentTimeISTString());
		head.put("version", paytmProperties.getVersion());

		JSONObject body = new JSONObject(initiateRequest.getPersistentAttributes());

		JSONObject merchantExtendedInfo = new JSONObject();
		merchantExtendedInfo.put("autoAccept", Boolean.TRUE.toString());
		merchantExtendedInfo.put("paymentMode", orderPaymentRequest.getPaymentModeName());
		body.put("merchantExtendedInfo", merchantExtendedInfo);

		JSONObject paytmParams = new JSONObject();
		paytmParams.put("body", body);
		paytmParams.put("head", head);

		String data = paytmParams.toString();
		log.info(data);
		String response = webClientService.postRequest(paytmProperties.getSaleApi(), data);
		log.info(response);
		PaytmEDCTransactionResponse paytmEDCTransactionResponse = ApplicationUtils.parseResponse(response,
				PaytmEDCTransactionResponse.class);
		if (Objects.isNull(paytmEDCTransactionResponse)) {
			throw new BaseException("Server Not Responding");
		} else if (!paytmEDCTransactionResponse.getBody().getResultInfo().getResultCode()
				.equalsIgnoreCase(ApplicationConstant.PAYTM_RESULT_CODE)) {
			throw new BaseException("Payment " + paytmEDCTransactionResponse.getBody().getResultInfo().getResultMsg());
		} else {
			getOrderPaymentDetail(orderPaymentRequest, paytmEDCTransactionResponse);
			paytmEDCTransactionResponse.getHead().setChecksum(paytmChecksum);
			paytmEDCTransactionResponse.getHead().setRequestTimeStamp(ApplicationUtils.getCurrentTimeISTString());
			initiateRequest.setPaytmEDCTransactionResponse(paytmEDCTransactionResponse);
			return initiateRequest;
		}
	}

	@Override
	public PaytmDQRCreateRequest initiateDqrTransaction(OrderPaymentRequest orderPaymentRequest) throws Exception {
		PaytmDQRCreateRequest initiateRequest = createPaytmDQRequest(orderPaymentRequest);
		JSONObject body = new JSONObject(initiateRequest.getPersistentAttributes());
		String paytmChecksum = PaytmChecksum.generateSignature(body.toString(), orderPaymentRequest.getMerchantKey());

		PaytmDQRRequestHeader header = PaytmDQRRequestHeader.builder().clientId(paytmProperties.getDqrClientId()).
				signature(paytmChecksum).channelId("WEB").version(paytmProperties.getDqrApiVersion()).build();

		JSONObject paytmParams = new JSONObject();
		paytmParams.put("body", body);
		paytmParams.put("head", new JSONObject(header.getPersistentAttributes()));

		String data = paytmParams.toString();
		log.info(data);
		String response = webClientService.postRequest(paytmProperties.getDqrSendApi(), data);
		log.info(response);
		PaytmDQRTransactionResponse paytmDQRTransactionResponse = ApplicationUtils.parseResponse(response,
				PaytmDQRTransactionResponse.class);
		if (Objects.isNull(paytmDQRTransactionResponse)) {
			throw new BaseException("Server Not Responding");
		} else if (!paytmDQRTransactionResponse.getBody().getResultInfo().getResultStatus()
				.equalsIgnoreCase(ApplicationConstant.PAYMENT_SUCCESS)) {
			throw new BaseException("Payment " + paytmDQRTransactionResponse.getBody().getResultInfo().getResultMsg());
		} else {
			getOrderPaymentDetail(orderPaymentRequest, paytmDQRTransactionResponse, initiateRequest);
			paytmDQRTransactionResponse.getHead().setSignature(paytmChecksum);
			paytmDQRTransactionResponse.getHead().setRequestTimestamp(ApplicationUtils.getCurrentTimeISTString());
			initiateRequest.setPaytmDQRTransactionResponse(paytmDQRTransactionResponse);
			return initiateRequest;
		}
	}

	@Override
	public Object updatePayment(PaytmEdcStatusRequest request, boolean skipSignatureVerification) throws Exception {
		String paytmChecksum = PaytmChecksum.generateSignature(request.getPersistentAttributes(),
				request.getMerchantKey());
		String response = webClientService.postRequest(paytmProperties.getStatusApi(),
				PaytmEDCStatusRequestBody.builder().head(PaytmEDCTransactionRequestHeader.builder()
						.requestTimeStamp(request.getPaytmEDCTransactionResponse().getHead().getRequestTimeStamp())
						.channelId("EDC").checksum(paytmChecksum).version(paytmProperties.getVersion()).build())
						.body(request.getPersistentAttributes()).build());
		PaytmEDCStatusResponse paytmEDCStatusResponse = ApplicationUtils.parseResponse(response,
				PaytmEDCStatusResponse.class);
		OrderPaymentDetailEntity orderPaymentDetail = orderPaymentDetailRepository
				.findByExternalOrderId(paytmEDCStatusResponse.getBody().getMerchantTransactionId());
		if (Objects.isNull(orderPaymentDetail)) {
			throw new BaseException("No Transaction Found with transaction number"
					+ paytmEDCStatusResponse.getBody().getMerchantTransactionId());
		}
		orderPaymentDetail.setPartnerTransactionId(paytmEDCStatusResponse.getBody().getAcquirementId());
		orderPaymentDetail.setPartnerOrderId(paytmEDCStatusResponse.getBody().getRetrievalReferenceNo());
		orderPaymentDetail.setPartnerPaymentStatus(paytmEDCStatusResponse.getBody().getResultInfo().getResultStatus());
		orderPaymentDetail.setResponseTime(ApplicationUtils.getCurrentTimestamp());
		orderPaymentDetail = orderPaymentDetailRepository.save(orderPaymentDetail);
		if (Objects.isNull(orderPaymentDetail.getOrderPaymentDetailId())) {
			throw new BaseException("Order Payment Request Updation Failed");
		}
		return paytmEDCStatusResponse;
	}

	@Override
	public Object updatePayment(PaytmDQRStatusRequest request, boolean skipSignatureVerification) throws Exception {
		if(Objects.isNull(request.getMerchantId())){
			request.setMerchantId(paytmProperties.getDqrMerchantId());
		}
		JSONObject body = new JSONObject(request.getPersistentAttributes());
		String paytmChecksum = PaytmChecksum.generateSignature(body.toString(),
				request.getMerchantKey());
		PaytmDQRRequestHeader head = PaytmDQRRequestHeader.builder().clientId(paytmProperties.getDqrClientId()).
				signature(paytmChecksum).channelId("WEB").version(paytmProperties.getDqrApiVersion()).build();

		JSONObject paytmParams = new JSONObject();
		paytmParams.put("body", body);
		paytmParams.put("head", new JSONObject(head.getPersistentAttributes()));

		String data = paytmParams.toString();
		log.info(data);
		String response = webClientService.postRequest(paytmProperties.getDqrStatusApi(), data);
		log.info(response);

		PaytmDQRStatusResponse paytmDQRStatusResponse = ApplicationUtils.parseResponse(response, PaytmDQRStatusResponse.class);
		OrderPaymentDetailEntity orderPaymentDetail = orderPaymentDetailRepository
				.findByExternalOrderId(paytmDQRStatusResponse.getBody().getOrderId());
		if (Objects.isNull(orderPaymentDetail)) {
			throw new BaseException("No Transaction Found with transaction number"
					+ paytmDQRStatusResponse.getBody().getOrderId());
		} else if (Objects.isNull(orderPaymentDetail.getPartnerPaymentStatus()) || !orderPaymentDetail.getPartnerPaymentStatus().equals(paytmDQRStatusResponse.getBody().getResultInfo().getResultStatus())) {
			orderPaymentDetail.setPartnerTransactionId(paytmDQRStatusResponse.getBody().getTxnId());
			orderPaymentDetail.setPartnerOrderId(paytmDQRStatusResponse.getBody().getMerchantUniqueReference());
			orderPaymentDetail.setPartnerPaymentStatus(paytmDQRStatusResponse.getBody().getResultInfo().getResultStatus());
			orderPaymentDetail.setResponseTime(ApplicationUtils.getCurrentTimestamp());
			orderPaymentDetail = orderPaymentDetailRepository.save(orderPaymentDetail);
			if (Objects.isNull(orderPaymentDetail.getOrderPaymentDetailId())) {
				throw new BaseException("Order Payment Request Updation Failed");
			}
		}
		return paytmDQRStatusResponse;
	}

	public PaytmRefundResponse refundPayment(PaytmRefundRequest refundRequest) throws Exception {
		log.info("Processing {} refund for orderId: {}", refundRequest.getPaymentPartnerType(), refundRequest.getOrderId());

		JSONObject body = new JSONObject(refundRequest.getPersistentAttributes());

		String paytmChecksum = PaytmChecksum.generateSignature(
				body.toString(),
				refundRequest.getMerchantKey()
		);

		JSONObject head = new JSONObject();
		head.put("signature", paytmChecksum);

		JSONObject paytmParams = new JSONObject();
		paytmParams.put("body", body);
		paytmParams.put("head", head);

		String requestData = paytmParams.toString();
		log.info("Paytm {} refund request: {}", refundRequest.getPaymentPartnerType(), requestData);

		String response = webClientService.postRequest(paytmProperties.getRefundApi(), requestData);
		log.info("Paytm {} refund response: {}", refundRequest.getPaymentPartnerType(), response);

		PaytmRefundResponse paytmRefundResponse = ApplicationUtils.parseResponse(response, PaytmRefundResponse.class);

		if (Objects.isNull(paytmRefundResponse)) {
			throw new BaseException("Server Not Responding");
		}

		if (Objects.isNull(paytmRefundResponse.getBody()) ||
				Objects.isNull(paytmRefundResponse.getBody().getResultInfo())) {
			throw new BaseException("Invalid response from Paytm refund API");
		}

		String resultStatus = paytmRefundResponse.getBody().getResultInfo().getResultStatus();
		String resultCode = paytmRefundResponse.getBody().getResultInfo().getResultCode();
		String resultMsg = paytmRefundResponse.getBody().getResultInfo().getResultMsg();

		log.info("Paytm {} refund result - Status: {}, Code: {}, Message: {}",
				refundRequest.getPaymentPartnerType(), resultStatus, resultCode, resultMsg);


		OrderPaymentDetailEntity orderPaymentDetail = orderPaymentDetailRepository
				.findByExternalOrderId(paytmRefundResponse.getBody().getOrderId());
		if (Objects.isNull(orderPaymentDetail)) {
			throw new BaseException("No Transaction Found with transaction number"
					+paytmRefundResponse.getBody().getOrderId());
		}
		orderPaymentDetail.setPartnerTransactionId(paytmRefundResponse.getBody().getRefundId());
		orderPaymentDetail.setPartnerOrderId(paytmRefundResponse.getBody().getTxnId());
		orderPaymentDetail.setPartnerPaymentStatus(paytmRefundResponse.getBody().getResultInfo().getResultStatus());
		orderPaymentDetail.setResponseTime(ApplicationUtils.getCurrentTimestamp());
		orderPaymentDetail.setRefundStatus(paytmRefundResponse.getBody().getResultInfo().getResultStatus());
		orderPaymentDetail = orderPaymentDetailRepository.save(orderPaymentDetail);
		if (Objects.isNull(orderPaymentDetail.getOrderPaymentDetailId())) {
			throw new BaseException("Order Payment Refund Request Failed");
		}

		refundRequest.setPaytmRefundResponse(paytmRefundResponse);

		return paytmRefundResponse;
	}

	private void getOrderPaymentDetail(OrderPaymentRequest orderPaymentRequest,
			PaytmEDCTransactionResponse paytmEDCTransactionResponse) {
		OrderPaymentDetailEntity detail = new OrderPaymentDetailEntity();
		if (orderPaymentRequest.getPaymentModeId() != PaymentPartner.INGENICO.getSystemId(null)) {
			cancelIfExist(paytmEDCTransactionResponse.getBody().getMerchantTransactionId());
		}
		detail.setExternalOrderId(paytmEDCTransactionResponse.getBody().getMerchantTransactionId());
		detail.setPaymentModeId(orderPaymentRequest.getPaymentModeId());
		detail.setPaymentSource(orderPaymentRequest.getPaymentSource().name());
		detail.setPaymentModeName(orderPaymentRequest.getPaymentModeName());
		detail.setPaymentStatus(paytmEDCTransactionResponse.getBody().getResultInfo().getResultStatus());
		detail.setRequestStatus(PaymentRequestStatus.INITIATED.name());
		detail.setRequestTime(AppUtils.getCurrentTimestamp());
		detail.setCustomerId(orderPaymentRequest.getCustomerId());
		detail.setCustomerName(orderPaymentRequest.getCustomerName());
		detail.setTransactionAmount(orderPaymentRequest.getPaidAmount());
		detail.setContactNumber(orderPaymentRequest.getContactNumber());
		detail.setMerchantId(paytmProperties.getMerchantId());
		detail.setRequestTerminal(Objects.nonNull(orderPaymentRequest.getRequestTerminal()) ? orderPaymentRequest.getRequestTerminal() : null);
		detail.setTransactionTerminal(Objects.nonNull(orderPaymentRequest.getTransactionTerminal()) ? orderPaymentRequest.getTransactionTerminal() : null);
		detail = orderPaymentDetailRepository.save(detail);
		if (Objects.isNull(detail.getOrderPaymentDetailId())) {
			throw new BaseException("Order Payment Request Failed");
		}
	}

	private void getOrderPaymentDetail(OrderPaymentRequest orderPaymentRequest,
									   PaytmDQRTransactionResponse paytmDQRTransactionResponse, PaytmDQRCreateRequest paytmDQRCreateRequest) {
		OrderPaymentDetailEntity detail = new OrderPaymentDetailEntity();
		if (orderPaymentRequest.getPaymentModeId() != PaymentPartner.INGENICO.getSystemId(null)) {
			cancelIfExist(paytmDQRCreateRequest.getOrderId());
		}
		detail.setExternalOrderId(paytmDQRCreateRequest.getOrderId());
		detail.setPaymentModeId(orderPaymentRequest.getPaymentModeId());
		detail.setPaymentSource(orderPaymentRequest.getPaymentSource().name());
		detail.setPaymentModeName(orderPaymentRequest.getPaymentModeName());
		detail.setPaymentStatus(paytmDQRTransactionResponse.getBody().getResultInfo().getResultStatus());
		detail.setRequestStatus(PaymentRequestStatus.INITIATED.name());
		detail.setRequestTime(AppUtils.getCurrentTimestamp());
		detail.setCustomerId(orderPaymentRequest.getCustomerId());
		detail.setCustomerName(orderPaymentRequest.getCustomerName());
		detail.setTransactionAmount(orderPaymentRequest.getPaidAmount());
		detail.setContactNumber(orderPaymentRequest.getContactNumber());
		detail.setMerchantId(paytmProperties.getDqrMerchantId());
		detail = orderPaymentDetailRepository.save(detail);
		if (Objects.isNull(detail.getOrderPaymentDetailId())) {
			throw new BaseException("Order Payment Request Failed");
		}
	}

	private void cancelIfExist(String merchantTransactionId) {
		List<OrderPaymentDetailEntity> orderPaymentDetailEntities = orderPaymentDetailRepository
				.findByExternalOrderIdAndRequestStatusNot(merchantTransactionId, PaymentRequestStatus.CANCELLED.name());
		if (!orderPaymentDetailEntities.isEmpty()) {
			orderPaymentDetailEntities.forEach(orderPaymentDetailEntity -> {
				orderPaymentDetailEntity.setRequestStatus(PaymentRequestStatus.CANCELLED.name());
				orderPaymentDetailEntity.setUpdateTime(AppUtils.getCurrentTimestamp());
				orderPaymentDetailEntity = orderPaymentDetailRepository.save(orderPaymentDetailEntity);
				if (Objects.isNull(orderPaymentDetailEntity.getOrderPaymentDetailId())) {
					throw new BaseException("Unable to Cancel Already Present Payment Request");
				}
			});
		}
	}

	private PaytmEdcCreateRequest createPaytmEDCRequest(OrderPaymentRequest orderPaymentRequest) {
		return PaytmEdcCreateRequest.builder().paytmMid(orderPaymentRequest.getPaytmMid()).paytmTid(orderPaymentRequest.getPaytmTid())
				.transactionAmount(String.valueOf(ApplicationUtils
						.multiply(orderPaymentRequest.getPaidAmount(), BigDecimal.valueOf(100)).intValue()))
				.merchantTransactionId(ApplicationUtils.getGeneratePartnerExternalOrderId(
						ApplicationConstant.KETTLE_ORDER, AppConstants.DATE_TIME_FORMATTER_WITH_NO_CHARACTERS))
				.autoAccept(Boolean.TRUE.toString()).paymentMode(orderPaymentRequest.getPaymentModeName()).build();
	}

	private PaytmDQRCreateRequest createPaytmDQRequest(OrderPaymentRequest orderPaymentRequest){
		return  PaytmDQRCreateRequest.builder().mid(orderPaymentRequest.getMerchantId()).orderId(ApplicationUtils.getGeneratePartnerExternalOrderId(
				ApplicationConstant.KETTLE_ORDER, AppConstants.DATE_TIME_FORMATTER_WITH_NO_CHARACTERS)).amount(String.valueOf(orderPaymentRequest.getPaidAmount())).posId(orderPaymentRequest.getPosId()).businessType(String.valueOf(PaymentMode.UPI_QR_CODE)).build();
	}

	@Override
	public PaytmRefundStatusResponse getRefundStatus(PaytmRefundStatusRequest refundStatusRequest) throws Exception {
		log.info("Processing {} refund status check for orderId: {}, refId: {}",
				refundStatusRequest.getPaymentPartnerType(),
				refundStatusRequest.getOrderId(),
				refundStatusRequest.getRefId());

		String paytmChecksum = PaytmChecksum.generateSignature(
				refundStatusRequest.getPersistentAttributes(),
				refundStatusRequest.getMerchantKey()
		);

		JSONObject body = new JSONObject(refundStatusRequest.getPersistentAttributes());

		JSONObject head = new JSONObject();
		head.put("signature", paytmChecksum);

		JSONObject paytmParams = new JSONObject();
		paytmParams.put("body", body);
		paytmParams.put("head", head);

		String requestData = paytmParams.toString();
		log.info("Paytm {} refund status request: {}", refundStatusRequest.getPaymentPartnerType(), requestData);

		String response = webClientService.postRequest(paytmProperties.getRefundStatusApi(), requestData);
		log.info("Paytm {} refund status response: {}", refundStatusRequest.getPaymentPartnerType(), response);

		PaytmRefundStatusResponse paytmRefundStatusResponse = ApplicationUtils.parseResponse(response, PaytmRefundStatusResponse.class);

		if (Objects.isNull(paytmRefundStatusResponse)) {
			throw new BaseException("Server Not Responding");
		}

		if (Objects.isNull(paytmRefundStatusResponse.getBody()) ||
				Objects.isNull(paytmRefundStatusResponse.getBody().getResultInfo())) {
			throw new BaseException("Invalid response from Paytm refund status API");
		}

		String resultStatus = paytmRefundStatusResponse.getBody().getResultInfo().getResultStatus();
		String resultCode = paytmRefundStatusResponse.getBody().getResultInfo().getResultCode();
		String resultMsg = paytmRefundStatusResponse.getBody().getResultInfo().getResultMsg();

		log.info("Paytm {} refund status result - Status: {}, Code: {}, Message: {}",
				refundStatusRequest.getPaymentPartnerType(), resultStatus, resultCode, resultMsg);

		refundStatusRequest.setPaytmRefundStatusResponse(paytmRefundStatusResponse);

		return paytmRefundStatusResponse;
	}
}
