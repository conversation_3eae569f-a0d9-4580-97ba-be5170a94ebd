package com.stpl.tech.pivot.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
public class PaytmRefundResponseBody {
    private String txnTimestamp;
    private String orderId;
    private String mid;
    private String refId;
    private PaytmEDCTransactionResultInfo resultInfo;
    private String refundId;
    private String txnId;
    private String refundAmount;
}
