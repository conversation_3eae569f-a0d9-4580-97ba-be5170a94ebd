package com.stpl.tech.pivot.domain;

import com.stpl.tech.master.payment.model.PaymentRequest;
import com.stpl.tech.pivot.core.payment.PaymentPartnerType;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.TreeMap;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PaytmRefundStatusRequest implements PaymentRequest {
    @NotNull(message = "MID cannot be null")
    private String mid;
    
    @NotNull(message = "Order ID cannot be null")
    private String orderId;
    
    @NotNull(message = "Refund ID cannot be null")
    private String refId;
    
    @NotNull(message = "Merchant Key cannot be null")
    private String merchantKey;
    
    private PaytmRefundStatusResponse paytmRefundStatusResponse;

    private PaymentPartnerType paymentPartnerType;

    @Override
    public String getPartnerOrderId() {
        return orderId;
    }

    @Override
    public TreeMap<String, String> getPersistentAttributes() {
        TreeMap<String, String> checkSumMap = new TreeMap<>();
        checkSumMap.put("mid", mid);
        checkSumMap.put("orderId", orderId);
        checkSumMap.put("refId", refId);
        return checkSumMap;
    }

    @Override
    public String getStatus() {
        return paytmRefundStatusResponse != null && paytmRefundStatusResponse.getBody() != null 
            ? paytmRefundStatusResponse.getBody().getResultInfo().getResultStatus() 
            : null;
    }
}
