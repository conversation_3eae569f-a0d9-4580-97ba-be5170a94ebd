package com.stpl.tech.pivot.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
public class RefundDetailInfo {
    private String refundType;
    private String payMethod;
    private String userCreditExpectedDate;
    private String userMobileNo;
    private String refundAmount;
}
