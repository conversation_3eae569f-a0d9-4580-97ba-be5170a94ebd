package com.stpl.tech.pivot.domain;

import com.stpl.tech.master.payment.model.PaymentRequest;
import com.stpl.tech.pivot.core.payment.PaymentPartnerType;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.TreeMap;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PaytmRefundRequest implements PaymentRequest {
    @NotNull(message = "MID cannot be null")
    private String mid;
    
    @NotNull(message = "Transaction Type cannot be null")
    private String txnType;
    
    @NotNull(message = "Order ID cannot be null")
    private String orderId;
    
    @NotNull(message = "Transaction ID cannot be null")
    private String txnId;
    
    @NotNull(message = "Refund ID cannot be null")
    private String refId;
    
    @NotNull(message = "Refund Amount cannot be null")
    private BigDecimal refundAmount;
    
    @NotNull(message = "Merchant Key cannot be null")
    private String merchantKey;
    
    private PaytmRefundResponse paytmRefundResponse;

    private PaymentPartnerType paymentPartnerType;

    @Override
    public String getPartnerOrderId() {
        return orderId;
    }

    @Override
    public TreeMap<String, String> getPersistentAttributes() {
        TreeMap<String, String> checkSumMap = new TreeMap<>();
        checkSumMap.put("mid", mid);
        checkSumMap.put("txnType", txnType);
        checkSumMap.put("orderId", orderId);
        checkSumMap.put("txnId", txnId);
        checkSumMap.put("refId", refId);
        checkSumMap.put("refundAmount", refundAmount.toString());
        return checkSumMap;
    }

    @Override
    public String getStatus() {
        return paytmRefundResponse != null && paytmRefundResponse.getBody() != null 
            ? paytmRefundResponse.getBody().getResultInfo().getResultStatus() 
            : null;
    }
}
