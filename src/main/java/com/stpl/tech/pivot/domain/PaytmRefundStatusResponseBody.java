package com.stpl.tech.pivot.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
public class PaytmRefundStatusResponseBody {
    private String orderId;
    private String userCreditInitiateStatus;
    private String mid;
    private String merchantRefundRequestTimestamp;
    private PaytmEDCTransactionResultInfo resultInfo;
    private String txnTimestamp;
    private String acceptRefundTimestamp;
    private String acceptRefundStatus;
    private List<RefundDetailInfo> refundDetailInfoList;
    private String userCreditInitiateTimestamp;
    private String totalRefundAmount;
    private String refId;
    private String txnAmount;
    private String refundId;
    private String txnId;
    private String refundAmount;
    private String refundReason;
    private AgentInfo agentInfo;
}
