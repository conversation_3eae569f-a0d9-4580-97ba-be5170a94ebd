package com.stpl.tech.pivot.task;

import com.kettle.tech.app.crm.model.payment.PaymentDetail;
import com.kettle.tech.app.crm.model.payment.RazorPayCreateRequest;
import com.kettle.tech.app.crm.model.payment.RazorPayPaymentResponse;
import com.kettle.tech.app.crm.model.payment.RazorPayPaymentStatus;
import com.kettle.tech.app.crm.service.PaymentService;
import com.stpl.tech.master.domain.model.PaymentRequest;
import com.stpl.tech.pivot.utils.PaymentUtil;
import com.stpl.tech.util.AppConstants;
import lombok.extern.slf4j.Slf4j;

import java.net.URISyntaxException;
import java.util.Objects;
import java.util.concurrent.DelayQueue;

@Slf4j
public class PaymentRefundTaskConsumer implements Runnable {


    PaymentService paymentService;

    private int ATTEMPT_DELAY;

    public PaymentRefundTaskConsumer(PaymentService paymentService, int attemptDelay) {
        this.paymentService = paymentService;
        this.ATTEMPT_DELAY = attemptDelay;
    }

    @Override
    public void run() {
        log.info("Inside run method of Payment refund task consumer");
        DelayQueue<DelayedPaymentTask> paymentTaskRefundQueue = PaymentUtil.paymentTaskRefundQueue;
        while (true) {
            try {
                DelayedPaymentTask delayedPaymentTask = paymentTaskRefundQueue.take();

                delayedPaymentTask.setCounter(delayedPaymentTask.getCounter() + 1);
                boolean succeed = false;
                PaymentRequest paymentRequest = delayedPaymentTask.getPaymentRequest();
                PaymentDetail paymentDetailObject = PaymentUtil.paymentDetailMap.get(paymentRequest.getOrderId());
                if (paymentRequest instanceof RazorPayCreateRequest) {
                    RazorPayCreateRequest razorPayCreateRequest = (RazorPayCreateRequest) paymentRequest;
                    try {
                        if (paymentProcessedByRazorPay(razorPayCreateRequest, Objects.nonNull(paymentDetailObject.getBrandId()) ?
                                paymentDetailObject.getBrandId() : AppConstants.CHAAYOS_BRAND_ID)) {
                            paymentService.processAsyncPaymentRefund(delayedPaymentTask.getCompanyId(),
                                    delayedPaymentTask.getPaymentRequest(), delayedPaymentTask.getPaymentPartner());
                        } else {
                            if (delayedPaymentTask.getCounter() < delayedPaymentTask.getMaxAttempt()) {
                                delayedPaymentTask.setDelayTime(ATTEMPT_DELAY);
                                paymentTaskRefundQueue.put(delayedPaymentTask);
                            }else{
                                log.info("Automatic payment refund for razorpay timed out for {}.",delayedPaymentTask.getPaymentRequest().getOrderId());
                            }
                        }
                    } catch (Exception e) {
                        log.error("Error while processing async payment", e);
                    }
                }
            } catch (InterruptedException e) {
                log.error("Error processing async order punching", e);
            }
        }
    }

    private boolean paymentProcessedByRazorPay(RazorPayCreateRequest razorPayCreateRequest,Integer brandId) throws URISyntaxException {
        log.info("Checking for payment status from RazorPay for payment transaction id {}", razorPayCreateRequest.getPaymentTransactionId());
        RazorPayPaymentResponse razorPayPaymentResponse = paymentService.fetchRazorPayPayment(razorPayCreateRequest.getPaymentTransactionId()
                ,brandId);
        log.info("Razor pay response ", razorPayPaymentResponse);
        if (razorPayPaymentResponse.getStatus().equalsIgnoreCase(RazorPayPaymentStatus.SUCCESSFUL.value()) ||
                razorPayPaymentResponse.getStatus().equalsIgnoreCase(RazorPayPaymentStatus.CAPTURED.value())
                || razorPayPaymentResponse.getStatus().equalsIgnoreCase(RazorPayPaymentStatus.AUTHORIZED.value())) {
            log.info("Payment accepted from razorpay ,status is {}", razorPayPaymentResponse.getStatus());
            return true;
        } else {
            log.info("Payment status from razorpay is still at CREATED for {}", razorPayCreateRequest.getPaymentTransactionId());
            return false;
        }
    }


}
