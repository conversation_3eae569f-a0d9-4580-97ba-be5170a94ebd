package com.stpl.tech.pivot.task;

import com.stpl.tech.kettle.delivery.model.PaymentPartner;
import com.stpl.tech.master.payment.model.PaymentRequest;
import com.stpl.tech.pivot.utils.ApplicationUtils;
import com.stpl.tech.util.AppUtils;

import java.util.concurrent.Delayed;
import java.util.concurrent.TimeUnit;

public class DelayedPaymentTask implements Delayed {

    private int counter = 0;

    private int MAX_ATTEMPT;

    private Integer companyId;

    private PaymentRequest paymentRequest;

    private long delayTime;

    private PaymentPartner paymentPartner;

    public DelayedPaymentTask(Integer companyId, PaymentRequest paymentRequest, long delayTime, AppUtils appUtils,
                              int maxAttempt, PaymentPartner paymentPartner) {
        this.companyId = companyId;
        this.paymentRequest = paymentRequest;
        this.delayTime = delayTime + ApplicationUtils.getCurrentTimestampInMilliSeconds();
        this.MAX_ATTEMPT = maxAttempt;
        this.paymentPartner = paymentPartner;
    }

    @Override
    public long getDelay(TimeUnit unit) {
        long difference = delayTime - appUtils.getCurrentTimestampInMilliSeconds();
        return unit.convert(difference, TimeUnit.MILLISECONDS);
    }

    @Override
    public int compareTo(Delayed o) {
        DelayedPaymentTask anotherTask = (DelayedPaymentTask) o;

        if (this.delayTime < anotherTask.delayTime) {
            return -1;
        }

        if (this.delayTime > anotherTask.delayTime) {
            return 1;
        }

        return 0;
    }

    public int getCounter() {
        return counter;
    }

    public void setCounter(int counter) {
        this.counter = counter;
    }

    public int getMaxAttempt() {
        return MAX_ATTEMPT;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public PaymentRequest getPaymentRequest() {
        return paymentRequest;
    }

    public void setPaymentRequest(PaymentRequest paymentRequest) {
        this.paymentRequest = paymentRequest;
    }

    public long getDelayTime() {
        return delayTime;
    }

    public void setDelayTime(long delayTime) {
        this.delayTime = delayTime + appUtils.getCurrentTimestampInMilliSeconds();
    }

    public PaymentPartner getPaymentPartner() {
        return paymentPartner;
    }

    public void setMAX_ATTEMPT(int MAX_ATTEMPT) {
        this.MAX_ATTEMPT = MAX_ATTEMPT;
    }
}

