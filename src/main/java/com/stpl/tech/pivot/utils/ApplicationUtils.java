package com.stpl.tech.pivot.utils;

import com.google.gson.Gson;
import com.stpl.tech.pivot.exceptions.BaseException;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormatter;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.Date;

public class ApplicationUtils extends AppUtils {
    public ApplicationUtils() {
        // TODO document why this constructor is empty
    }

    public static String generateRandomString() {
        final String characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        SecureRandom secureRandom = new SecureRandom();
        StringBuilder stringBuilder = new StringBuilder(5);
        for (int i = 0; i < 5; i++) {
            int randomIndex = secureRandom.nextInt(characters.length());
            stringBuilder.append(characters.charAt(randomIndex));
        }
        return stringBuilder.toString();
    }

    public static <T> T parseResponse(String response, Class<T> c) {
        Gson gson = new Gson();
        return gson.fromJson(response, c);
    }

    public static String getCurrentTimeISTString() {
        return AppUtils.getCurrentTimeIST().toString(AppConstants.DATE_TIME_WITH_NO_MILISECOND_FORMATTER);
    }

    public static String getCurrentTimeISTString(DateTimeFormatter format) {
        return AppUtils.getCurrentTimeIST().toString(format);
    }


    public static String getGeneratePartnerExternalOrderId(String module, DateTimeFormatter format) {
        return module+ getCurrentTimeISTString(format) +  generateRandomString().toUpperCase();
    }

    public static Date timeWithMilliseconds(DateTime date , long milliSeconds){
        long time = date.getMillis() + milliSeconds;
        return new Date(time);
    }

    public static long getCurrentTimestampInMilliSeconds(){
        return getCurrentTimeIST().getMillis();
    }

    public static void main(String[] args) {
        System.out.println(getGeneratePartnerExternalOrderId(ApplicationConstant.KETTLE_ORDER, AppConstants.DATE_TIME_FORMATTER_WITH_NO_CHARACTERS));
    }
}
