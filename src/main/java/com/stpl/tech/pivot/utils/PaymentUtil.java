package com.stpl.tech.pivot.utils;

import com.stpl.tech.master.payment.model.PaymentDetail;
import com.stpl.tech.pivot.task.DelayedPaymentTask;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.DelayQueue;

public class PaymentUtil {

    public static ConcurrentHashMap<String, PaymentDetail> paymentDetailMap = new ConcurrentHashMap<>();

    public static DelayQueue<DelayedPaymentTask> paymentTaskRefundQueue = new DelayQueue<>();

}
