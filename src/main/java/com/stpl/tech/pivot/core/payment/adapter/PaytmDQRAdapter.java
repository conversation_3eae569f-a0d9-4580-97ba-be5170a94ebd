package com.stpl.tech.pivot.core.payment.adapter;

import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.pivot.core.payment.PaymentAdapter;
import com.stpl.tech.pivot.domain.PaytmDQRCreateRequest;
import com.stpl.tech.pivot.domain.PaytmDQRStatusRequest;
import com.stpl.tech.pivot.domain.PaytmRefundRequest;
import com.stpl.tech.pivot.domain.PaytmRefundStatusRequest;
import com.stpl.tech.pivot.service.PaytmPaymentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;


@Service
public class PaytmDQRAdapter extends PaymentAdapter<OrderPaymentRequest, PaytmDQRCreateRequest> {

    @Autowired
    PaytmPaymentService paytmPaymentService;

    @Override
    public PaytmDQRCreateRequest createPaymentRequest(OrderPaymentRequest orderPaymentRequest, Map<String, String> map) throws Exception {
       return paytmPaymentService.initiateDqrTransaction(orderPaymentRequest);
    }

    @Override
    public Object updatePayment(Object object, boolean skipSignatureVerification) throws Exception {
        PaytmDQRStatusRequest request = (PaytmDQRStatusRequest) object;
        return paytmPaymentService.updatePayment(request,skipSignatureVerification);
    }

    @Override
    public Object refundPayment(Object object) throws Exception {
        PaytmRefundRequest request = (PaytmRefundRequest)object;
        return paytmPaymentService.refundPayment(request);
    }

    @Override
    public Object getRefundStatus(Object object) throws Exception {
        PaytmRefundStatusRequest request = (PaytmRefundStatusRequest)object;
        return paytmPaymentService.getRefundStatus(request);
    }
}
